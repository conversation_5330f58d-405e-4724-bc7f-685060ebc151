backend:
  name: git-gateway
  branch: main # or master, depending on your default branch

# Media folder where uploads will be stored
# Path is relative to this config file. Since this file lives in
# `public/admin/cms`, we go two directories up to reach the project root
# and point to the `public/uploads` directory.
media_folder: public/uploads
# Does this even exists/matter? I only found media_folder in original netlify-cms github repos config.yaml
public_folder: public

# Collections define the structure for content
collections:
  - name: 'news'
    label: 'News'
    folder: 'src/content/news'
    create: true
    slug: '{{year}}-{{month}}-{{day}}-{{slug}}'
    fields:
      - { label: 'Titel', name: 'title', widget: 'string' }
      - { label: 'Veröffentlichungsdatum', name: 'date', widget: 'datetime' }
      - { label: 'Bild', name: 'image', widget: 'image', required: false }
      - { label: 'Kurzbeschreibung', name: 'excerpt', widget: 'text' }
      - { label: 'Inhalt', name: 'body', widget: 'markdown' }
      - { label: 'Tags', name: 'tags', widget: 'list', required: false }
      - { label: 'Autor', name: 'author', widget: 'string', required: false }

  - name: 'teams'
    label: 'Mannschaften'
    folder: 'src/content/teams'
    create: true
    slug: '{{slug}}'
    fields:
      - { label: 'Name', name: 'title', widget: 'string' }
      - {
          label: 'Kategorie',
          name: 'category',
          widget: 'select',
          options: ['Kampfmannschaft', 'U23', 'Nachwuchs', 'Special Kickers'],
        }
      - { label: 'Bild', name: 'image', widget: 'image', required: false }
      - { label: 'Trainer', name: 'coach', widget: 'string' }
      - { label: 'Co-Trainer', name: 'assistantCoach', widget: 'string', required: false }
      - { label: 'Trainingszeiten', name: 'trainingTimes', widget: 'string' }
      - { label: 'Beschreibung', name: 'body', widget: 'markdown' }
      - {
          label: 'Spieler',
          name: 'players',
          widget: 'list',
          required: false,
          fields:
            [
              { label: 'Name', name: 'name', widget: 'string' },
              { label: 'Position', name: 'position', widget: 'string' },
              { label: 'Geburtsdatum', name: 'birthdate', widget: 'string', required: false },
              { label: 'Bild', name: 'image', widget: 'image', required: false },
            ],
        }
      - { label: 'Reihenfolge', name: 'order', widget: 'number', required: true }

  - name: 'sponsors'
    label: 'Sponsoren'
    folder: 'src/content/sponsors'
    create: true
    slug: '{{slug}}'
    fields:
      - { label: 'Name', name: 'title', widget: 'string' }
      - { label: 'Logo', name: 'logo', widget: 'image' }
      - { label: 'Website', name: 'website', widget: 'string', required: false }
      - { label: 'Platzierung', name: 'order', widget: 'number', required: true }
      - { label: 'Beschreibung', name: 'body', widget: 'markdown', required: false }

  - name: 'products'
    label: 'Shop Produkte'
    folder: 'src/content/products'
    create: true
    slug: '{{slug}}'
    fields:
      - { label: 'Name', name: 'title', widget: 'string' }
      - { label: 'Preis', name: 'price', widget: 'number', value_type: 'float' }
      - { label: 'Bild', name: 'image', widget: 'image' }
      - {
          label: 'Kategorie',
          name: 'category',
          widget: 'select',
          options: ['Trikots', 'Trainingskleidung', 'Fanartikel', 'Ausrüstung'],
        }
      - { label: 'Beschreibung', name: 'body', widget: 'markdown' }
      - { label: 'Verfügbar', name: 'available', widget: 'boolean', default: true }
      - { label: 'Größen', name: 'sizes', widget: 'list', required: false }
      - { label: 'Farben', name: 'colors', widget: 'list', required: false }

  - name: 'pages'
    label: 'Seiten'
    folder: 'src/content/pages'
    create: false
    slug: '{{slug}}'
    fields:
      - { label: 'Titel', name: 'title', widget: 'string' }
      - { label: 'Hero Titel', name: 'heroTitle', widget: 'string', required: false }
      - { label: 'Hero Untertitel', name: 'heroSubtitle', widget: 'string', required: false }
      - { label: 'Hero Bild', name: 'heroImage', widget: 'image', required: false }
      - { label: 'Über uns Text', name: 'aboutText', widget: 'text', required: false }
      - { label: 'CTA Text', name: 'ctaText', widget: 'string', required: false }
      - { label: 'CTA Untertitel', name: 'ctaSubtext', widget: 'string', required: false }
      - { label: 'Jahre Erfahrung', name: 'jahreErfahrung', widget: 'string', required: false }
      - { label: 'Anzahl Teams', name: 'teamCount', widget: 'number', required: false }
      - { label: 'Aktive Spieler', name: 'activePlayerCount', widget: 'string', required: false }
      - { label: 'Zähler anzeigen', name: 'counter', widget: 'boolean', required: false }
      - {
          label: 'Zähler Zieldatum',
          name: 'eventCounterDate',
          widget: 'datetime',
          format: 'DD.MM.YYYY',
          required: false,
        }
      - { label: 'Einleitung', name: 'intro', widget: 'text', required: false }
      - { label: 'Adresse', name: 'address', widget: 'text', required: false }
      - { label: 'Telefon', name: 'phone', widget: 'string', required: false }
      - { label: 'E-Mail', name: 'email', widget: 'string', required: false }
      - { label: 'Öffnungszeiten', name: 'openingHours', widget: 'text', required: false }
      - { label: 'Google Maps Link', name: 'mapsLink', widget: 'string', required: false }
      - {
          label: 'Kontaktformular Aktiviert',
          name: 'formEnabled',
          widget: 'boolean',
          required: false,
        }
      - {
          label: 'Formular Erfolgstext',
          name: 'formSuccessMessage',
          widget: 'string',
          required: false,
        }
      - { label: 'Inhalt', name: 'body', widget: 'markdown', required: false }

  - name: 'anmeldung'
    label: 'Anmeldungen'
    folder: 'src/content/anmeldung'
    create: true
    slug: '{{slug}}'
    fields:
      - { label: 'Titel', name: 'title', widget: 'string' }
      - { label: 'Beschreibung', name: 'description', widget: 'text' }
      - { label: 'Bild', name: 'image', widget: 'image', required: false }
      - { label: 'Hero Bild', name: 'heroImage', widget: 'image', required: false }
      - { label: 'Link', name: 'link', widget: 'string' }
      - { label: 'Datum', name: 'date', widget: 'datetime' }
      - { label: 'Enddatum', name: 'endDate', widget: 'datetime', required: false }
      - { label: 'Reihenfolge', name: 'order', widget: 'number', required: true }
      - { label: 'Anmeldung aktiv', name: 'active', widget: 'boolean', default: true }

  - name: 'events'
    label: 'Veranstaltungen'
    folder: 'src/content/events'
    create: true
    slug: '{{year}}-{{slug}}'
    fields:
      - { label: 'Titel', name: 'title', widget: 'string' }
      - { label: 'Datum', name: 'date', widget: 'datetime' }
      - { label: 'Enddatum', name: 'endDate', widget: 'datetime', required: false }
      - { label: 'Ort', name: 'location', widget: 'string' }
      - { label: 'Bild', name: 'image', widget: 'image', required: false }
      - { label: 'Kurzbeschreibung', name: 'excerpt', widget: 'text' }
      - { label: 'Inhalt', name: 'body', widget: 'markdown' }
      - { label: 'Hervorgehoben', name: 'featured', widget: 'boolean', default: false }
      - {
          label: 'Kategorie',
          name: 'category',
          widget: 'select',
          options: ['Spiel', 'Training', 'Feier', 'Sonstiges'],
          default: 'Sonstiges',
        }
      - label: 'Dienstplan'
        name: 'schedule'
        widget: 'list'
        required: false
        fields:
          - { label: 'Datum', name: 'date', widget: 'string' }
          - { label: 'Wochentag', name: 'day', widget: 'string' }
          - label: 'Schichten'
            name: 'shifts'
            widget: 'list'
            fields:
              - { label: 'Zeit', name: 'time', widget: 'string' }
              - { label: 'Helfer', name: 'volunteer', widget: 'string', required: false }

  - name: 'countdowns'
    label: 'Countdown-Events'
    folder: 'src/content/countdowns'
    create: true
    # preview_path: "countdown/{{slug}}"   # optional, SEO-Preview-URL
    preview: true
    slug: '{{slug}}'
    fields:
      - { label: 'Aktiv', name: 'active', widget: 'boolean', default: false }
      - { label: 'Titel', name: 'title', widget: 'string' }
      - { label: 'Beschreibung', name: 'description', widget: 'text', required: false }
      - { label: 'Zieldatum', name: 'date', widget: 'datetime', format: 'YYYY-MM-DDTHH:mm:ssZ' }

  - name: 'verein'
    label: 'Verein'
    folder: 'src/content/verein'
    create: true
    slug: '{{slug}}'
    fields:
      - { label: 'Titel', name: 'title', widget: 'string' }
      - { label: 'Hero Titel', name: 'heroTitle', widget: 'string', required: false }
      - { label: 'Hero Untertitel', name: 'heroSubtitle', widget: 'string', required: false }
      - { label: 'Hero Bild', name: 'heroImage', widget: 'image', required: false }
      - { label: 'Über uns Text', name: 'aboutText', widget: 'text', required: false }
      - { label: 'CTA Text', name: 'ctaText', widget: 'string', required: false }
      - { label: 'CTA Untertitel', name: 'ctaSubtext', widget: 'string', required: false }
      - { label: 'Jahre Erfahrung', name: 'jahreErfahrung', widget: 'string', required: false }
      - { label: 'Anzahl Teams', name: 'teamCount', widget: 'number', required: false }
      - { label: 'Aktive Spieler', name: 'activePlayerCount', widget: 'string', required: false }
      - { label: 'Zähler anzeigen', name: 'counter', widget: 'boolean', required: false }
      - { label: 'Zähler Zieldatum', name: 'eventCounterDate', widget: 'datetime', format: 'DD.MM.YYYY', required: false }
      - { label: 'Einleitung', name: 'intro', widget: 'text', required: false }
      - { label: 'Adresse', name: 'address', widget: 'text', required: false }
      - { label: 'Telefon', name: 'phone', widget: 'string', required: false }
      - { label: 'E-Mail', name: 'email', widget: 'string', required: false }
      - { label: 'Öffnungszeiten', name: 'openingHours', widget: 'text', required: false }
      - { label: 'Google Maps Link', name: 'mapsLink', widget: 'string', required: false }
      - { label: 'Kontaktformular Aktiviert', name: 'formEnabled', widget: 'boolean', required: false }
      - { label: 'Formular Erfolgstext', name: 'formSuccessMessage', widget: 'string', required: false }
      - { label: 'Inhalt', name: 'body', widget: 'markdown', required: false }

  - name: 'galleries'
    label: 'Galerien'
    folder: 'src/content/galleries'
    create: true
    slug: '{{slug}}'
    fields:
      - { label: 'Titel', name: 'title', widget: 'string' }
      - { label: 'Beschreibung', name: 'description', widget: 'text' }
      - { label: 'Vorschaubild', name: 'previewImage', widget: 'image' }
      - {
          label: 'Kategorie',
          name: 'category',
          widget: 'select',
          options: ['Teams', 'Events', 'Training', 'Spiele', 'Vereinsleben', 'Sonstiges'],
          default: 'Sonstiges',
        }
      - { label: 'Datum', name: 'date', widget: 'datetime', required: false }
      - { label: 'Hervorgehoben', name: 'featured', widget: 'boolean', default: false }
      - { label: 'Veröffentlicht', name: 'published', widget: 'boolean', default: true }
      - { label: 'Reihenfolge', name: 'order', widget: 'number', default: 0 }
      - label: 'Bilder'
        name: 'images'
        widget: 'list'
        fields:
          - { label: 'Bild', name: 'src', widget: 'image' }
          - { label: 'Alt-Text', name: 'alt', widget: 'string', required: false }
          - { label: 'Bildunterschrift', name: 'caption', widget: 'string', required: false }
          - { label: 'Reihenfolge', name: 'order', widget: 'number', required: false }
      - { label: 'Inhalt', name: 'body', widget: 'markdown', required: false }

  - name: 'settings'
    label: 'Einstellungen'
    files:
      - label: 'Navigation'
        name: 'navigation'
        file: 'src/content/settings/navigation.md'
        fields:
          - label: 'Hauptmenü'
            name: 'mainMenu'
            widget: 'list'
            fields:
              - { label: 'Text', name: 'text', widget: 'string' }
              - { label: 'URL', name: 'url', widget: 'string' }
              - label: 'Untermenü'
                name: 'submenu'
                widget: 'list'
                required: false
                fields:
                  - { label: 'Text', name: 'text', widget: 'string' }
                  - { label: 'URL', name: 'url', widget: 'string' }

      - label: 'Footer'
        name: 'footer'
        file: 'src/content/settings/footer.md'
        fields:
          - { label: 'Copyright Text', name: 'copyright', widget: 'string' }
          - label: 'Social Media'
            name: 'socialMedia'
            widget: 'list'
            fields:
              - {
                  label: 'Plattform',
                  name: 'platform',
                  widget: 'select',
                  options: ['Facebook', 'Instagram', 'Twitter', 'YouTube'],
                }
              - { label: 'URL', name: 'url', widget: 'string' }
              - { label: 'Icon', name: 'icon', widget: 'string', default: 'fa-facebook' }
          - label: 'Footer Links'
            name: 'links'
            widget: 'list'
            fields:
              - { label: 'Text', name: 'text', widget: 'string' }
              - { label: 'URL', name: 'url', widget: 'string' }
